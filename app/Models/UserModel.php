<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table            = 'users';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'username', 'email', 'password', 'first_name', 'last_name',
        'role', 'status', 'avatar', 'bio', 'last_login', 'email_verified_at', 'remember_token'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username,id,{id}]',
        'email' => 'required|valid_email|is_unique[users.email,id,{id}]',
        'password' => 'required|min_length[6]',
        'first_name' => 'permit_empty|max_length[50]',
        'last_name' => 'permit_empty|max_length[50]',
        'role' => 'required|in_list[admin,editor,author,subscriber]',
        'status' => 'required|in_list[active,inactive,banned]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['hashPassword'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['hashPassword'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Hash password before insert/update
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Verify user credentials
     */
    public function verifyCredentials(string $username, string $password): ?array
    {
        $user = $this->where('username', $username)
                     ->orWhere('email', $username)
                     ->where('status', 'active')
                     ->first();

        if ($user && password_verify($password, $user['password'])) {
            // Update last login
            $this->update($user['id'], ['last_login' => date('Y-m-d H:i:s')]);
            unset($user['password']); // Remove password from returned data
            return $user;
        }

        return null;
    }

    /**
     * Check if user has permission
     */
    public function hasPermission(int $userId, string $permission): bool
    {
        $user = $this->find($userId);
        if (!$user) return false;

        $rolePermissions = [
            'admin' => ['*'], // Admin has all permissions
            'editor' => ['pages.create', 'pages.edit', 'pages.delete', 'blog.create', 'blog.edit', 'blog.delete', 'media.upload', 'media.delete'],
            'author' => ['pages.create', 'pages.edit', 'blog.create', 'blog.edit', 'media.upload'],
            'subscriber' => ['profile.edit']
        ];

        $userRole = $user['role'];
        $permissions = $rolePermissions[$userRole] ?? [];

        return in_array('*', $permissions) || in_array($permission, $permissions);
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $role): array
    {
        return $this->where('role', $role)->where('status', 'active')->findAll();
    }
}
