<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Authentication Routes
$routes->group('auth', function($routes) {
    $routes->get('login', 'AuthController::login');
    $routes->post('login', 'AuthController::processLogin');
    $routes->get('register', 'AuthController::register');
    $routes->post('register', 'AuthController::processRegister');
    $routes->get('logout', 'AuthController::logout');
    $routes->get('check', 'AuthController::checkAuth');
});

// Admin Routes (Protected)
$routes->group('admin', ['filter' => 'auth'], function($routes) {
    $routes->get('dashboard', 'Admin\DashboardController::index');

    // Page Management
    $routes->group('pages', function($routes) {
        $routes->get('/', 'Admin\PageController::index');
        $routes->get('create', 'Admin\PageController::create', ['filter' => 'permission:pages.create']);
        $routes->post('store', 'Admin\PageController::store', ['filter' => 'permission:pages.create']);
        $routes->get('edit/(:num)', 'Admin\PageController::edit/$1', ['filter' => 'permission:pages.edit']);
        $routes->post('update/(:num)', 'Admin\PageController::update/$1', ['filter' => 'permission:pages.edit']);
        $routes->delete('delete/(:num)', 'Admin\PageController::delete/$1', ['filter' => 'permission:pages.delete']);
        $routes->get('preview/(:num)', 'Admin\PageController::preview/$1');
    });

    // Menu Management
    $routes->group('menus', function($routes) {
        $routes->get('/', 'Admin\MenuController::index');
        $routes->get('create', 'Admin\MenuController::create', ['filter' => 'permission:menus.create']);
        $routes->post('store', 'Admin\MenuController::store', ['filter' => 'permission:menus.create']);
        $routes->get('edit/(:num)', 'Admin\MenuController::edit/$1', ['filter' => 'permission:menus.edit']);
        $routes->post('update/(:num)', 'Admin\MenuController::update/$1', ['filter' => 'permission:menus.edit']);
        $routes->delete('delete/(:num)', 'Admin\MenuController::delete/$1', ['filter' => 'permission:menus.delete']);
        $routes->get('builder/(:num)', 'Admin\MenuController::builder/$1', ['filter' => 'permission:menus.edit']);
        $routes->post('duplicate/(:num)', 'Admin\MenuController::duplicate/$1', ['filter' => 'permission:menus.create']);
    });

    // Menu Item Management
    $routes->group('menu-items', function($routes) {
        $routes->post('store', 'Admin\MenuItemController::store', ['filter' => 'permission:menus.edit']);
        $routes->post('update/(:num)', 'Admin\MenuItemController::update/$1', ['filter' => 'permission:menus.edit']);
        $routes->delete('delete/(:num)', 'Admin\MenuItemController::delete/$1', ['filter' => 'permission:menus.edit']);
        $routes->get('get/(:num)', 'Admin\MenuItemController::get/$1');
        $routes->post('update-order', 'Admin\MenuItemController::updateOrder', ['filter' => 'permission:menus.edit']);
        $routes->get('menu/(:num)', 'Admin\MenuItemController::getMenuItems/$1');
        $routes->post('bulk-status', 'Admin\MenuItemController::bulkUpdateStatus', ['filter' => 'permission:menus.edit']);
        $routes->get('search-pages', 'Admin\MenuItemController::searchPages');
    });

    // Blog Management
    $routes->group('blog', function($routes) {
        $routes->get('/', 'Admin\BlogController::index');
        $routes->get('create', 'Admin\BlogController::create');
        $routes->post('store', 'Admin\BlogController::store');
        $routes->get('edit/(:num)', 'Admin\BlogController::edit/$1');
        $routes->post('update/(:num)', 'Admin\BlogController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\BlogController::delete/$1');
        $routes->post('duplicate/(:num)', 'Admin\BlogController::duplicate/$1');
        $routes->post('bulk-action', 'Admin\BlogController::bulkAction');
        $routes->get('search', 'Admin\BlogController::search');
        $routes->get('by-status', 'Admin\BlogController::getPostsByStatus');
    });

    // Category Management
    $routes->group('categories', function($routes) {
        $routes->get('/', 'Admin\CategoryController::index');
        $routes->get('create', 'Admin\CategoryController::create');
        $routes->post('store', 'Admin\CategoryController::store');
        $routes->get('edit/(:num)', 'Admin\CategoryController::edit/$1');
        $routes->post('update/(:num)', 'Admin\CategoryController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\CategoryController::delete/$1');
        $routes->post('reorder', 'Admin\CategoryController::reorder');
    });

    // Tag Management
    $routes->group('tags', function($routes) {
        $routes->get('/', 'Admin\TagController::index');
        $routes->get('create', 'Admin\TagController::create');
        $routes->post('store', 'Admin\TagController::store');
        $routes->get('edit/(:num)', 'Admin\TagController::edit/$1');
        $routes->post('update/(:num)', 'Admin\TagController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\TagController::delete/$1');
        $routes->get('search', 'Admin\TagController::search');
    });

    // Comment Management
    $routes->group('comments', function($routes) {
        $routes->get('/', 'Admin\CommentController::index');
        $routes->get('pending', 'Admin\CommentController::pending');
        $routes->post('approve/(:num)', 'Admin\CommentController::approve/$1');
        $routes->post('spam/(:num)', 'Admin\CommentController::spam/$1');
        $routes->post('trash/(:num)', 'Admin\CommentController::trash/$1');
        $routes->delete('delete/(:num)', 'Admin\CommentController::delete/$1');
        $routes->post('bulk-action', 'Admin\CommentController::bulkAction');
    });
});
